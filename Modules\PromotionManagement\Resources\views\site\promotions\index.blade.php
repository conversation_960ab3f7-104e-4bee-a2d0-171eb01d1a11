@extends('site.layouts.site_layout')

@section('title', 'العروض الترويجية - موتور لاين')
@section('meta_description', 'تصفح أحدث العروض الترويجية على السيارات في موتور لاين')

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4 text-center">العروض الترويجية</h1>
            <p class="text-center text-muted mb-5">اكتشف أفضل العروض والخصومات على السيارات</p>
        </div>
    </div>

    @if($promotions->count() > 0)
        <div class="row">
            @foreach($promotions as $promotion)
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        @if($promotion->banner_image_url)
                            <img src="{{ $promotion->banner_image_url }}" 
                                 class="card-img-top" 
                                 alt="{{ $promotion->name }}"
                                 style="height: 250px; object-fit: cover;">
                        @else
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 250px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ $promotion->name }}</h5>
                            
                            @if($promotion->description)
                                <p class="card-text text-muted">{{ Str::limit($promotion->description, 100) }}</p>
                            @endif
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        ينتهي في {{ $promotion->end_date->format('Y/m/d') }}
                                    </small>
                                    @if($promotion->days_remaining <= 7)
                                        <span class="badge bg-warning text-dark">
                                            {{ $promotion->days_remaining }} أيام متبقية
                                        </span>
                                    @endif
                                </div>
                                
                                <a href="#" class="btn btn-primary w-100">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-center">
                    {{ $promotions->links() }}
                </div>
            </div>
        </div>
    @else
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">لا توجد عروض متاحة حالياً</h3>
                    <p class="text-muted">يرجى التحقق مرة أخرى قريباً للاطلاع على أحدث العروض!</p>
                    <a href="{{ route('site.home') }}" class="btn btn-primary mt-3">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection

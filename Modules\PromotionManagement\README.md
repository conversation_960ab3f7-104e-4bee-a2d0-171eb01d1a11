# وحدة إدارة العروض الترويجية - PromotionManagement Module

## نظرة عامة

وحدة إدارة العروض الترويجية هي جزء من نظام MotorLine لإدارة معارض السيارات. تتولى هذه الوحدة إدارة العروض الترويجية على السيارات وعرضها في الموقع العام.

## الميزات المنفذة

### 1. عرض قائمة العروض الترويجية في الموقع العام
- **المهمة**: `PH03-TASK-038` - `BE-CTRL-SITE-PROMOTIONS-LIST-DISPLAY-001`
- **الهدف**: إنشاء Controller action لعرض قائمة العروض الترويجية النشطة
- **الميزات**:
  - عرض العروض النشطة والجارية فقط
  - فلترة العروض حسب التاريخ والحالة
  - عرض صور بنرات العروض
  - ترقيم النتائج (10 عناصر لكل صفحة)
  - تصميم متجاوب
  - حالة فارغة عند عدم وجود عروض

## الهيكل التقني

### Models
- **Promotion**: نموذج العروض الترويجية
  - دعم الترجمة باستخدام `spatie/laravel-translatable`
  - دعم الصور باستخدام `spatie/laravel-medialibrary`
  - علاقة كثير لكثير مع السيارات
  - نطاقات للفلترة (active, current, activeAndCurrent)
  - خصائص محسوبة للصور والتواريخ

### Controllers
- **SitePromotionController**: Controller الموقع العام
  - دالة `index()` لعرض قائمة العروض النشطة
  - استخدام eager loading للأداء
  - ترقيم النتائج

### Views
- **site/promotions/index.blade.php**: صفحة قائمة العروض

### Database
- **promotions**: جدول العروض الترويجية
- **car_promotion**: جدول ربط السيارات بالعروض (pivot table)

## المسارات

### مسارات الموقع العام
```php
// عرض قائمة العروض الترويجية
GET /promotions
```

## التثبيت والإعداد

### 1. تشغيل Migrations
```bash
php artisan migrate --path=Modules/PromotionManagement/Database/Migrations
```

### 2. تفعيل الموديول
```bash
php artisan module:enable PromotionManagement
```

## الاستخدام

### إضافة عرض ترويجي جديد
```php
use Modules\PromotionManagement\Models\Promotion;

$promotion = Promotion::create([
    'name' => ['ar' => 'عرض نهاية العام'],
    'description' => ['ar' => 'خصومات هائلة على جميع السيارات'],
    'start_date' => now(),
    'end_date' => now()->addDays(30),
    'status' => true,
]);
```

### إضافة صورة بنر للعرض
```php
$promotion->addMediaFromRequest('banner')
        ->toMediaCollection('promotion_banners');
```

### جلب العروض النشطة والجارية
```php
$promotions = Promotion::activeAndCurrent()
                      ->with('media')
                      ->orderBy('created_at', 'desc')
                      ->paginate(10);
```

### ربط سيارة بعرض ترويجي
```php
$promotion->cars()->attach($carId, [
    'car_offer_price' => 95000.00
]);
```

## المتطلبات

### الحزم المطلوبة
- `spatie/laravel-translatable`: للترجمة
- `spatie/laravel-medialibrary`: لإدارة الصور
- `nwidart/laravel-modules`: لإدارة الموديولات

### إعدادات قاعدة البيانات
- دعم JSON للترجمة
- دعم Foreign Keys
- فهارس للأداء على حقول التاريخ والحالة

## الأمان

### الحماية المطبقة
- عرض العروض النشطة والجارية فقط
- التحقق من صحة التواريخ
- حماية من SQL Injection

## الأداء

### التحسينات المطبقة
- استخدام Eager Loading للصور
- فهرسة الحقول المهمة (status, start_date, end_date)
- فهرس مركب للاستعلامات المتكررة
- ترقيم النتائج

### التخزين المؤقت
- يمكن إضافة تخزين مؤقت للعروض النشطة مستقبلاً

## الميزات القادمة

### المرحلة التالية
1. **صفحة تفاصيل العرض**: عرض تفاصيل العرض والسيارات المشمولة
2. **إدارة العروض**: لوحة تحكم لإدارة العروض
3. **ربط العروض بالسيارات**: واجهة لربط السيارات بالعروض
4. **إحصائيات العروض**: تقارير عن أداء العروض

### تحسينات مستقبلية
- نظام إشعارات انتهاء العروض
- عروض مخصصة للعملاء
- تكامل مع نظام الدفع
- عروض محدودة الكمية

## هيكل الملفات
```
Modules/PromotionManagement/
├── Config/
├── Database/
│   ├── Migrations/
│   └── Seeders/
├── Http/
│   └── Controllers/
│       └── Site/
├── Models/
├── Resources/
│   └── views/
│       └── site/
│           └── promotions/
└── Routes/
```

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
